import React, { useState } from "react";
import { Modal, Typography, Space, Button } from "antd";
import InstallProgressDialog from "./InstallProgressDialog";
import OfflineInstallDialog from "./OfflineInstallDialog";

const { Title, Paragraph, Text } = Typography;

interface PlaywrightInstallDialogProps {
  visible: boolean;
  onClose: () => void;
}

const PlaywrightInstallDialog: React.FC<PlaywrightInstallDialogProps> = ({
  visible,
  onClose,
}) => {
  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [showOfflineDialog, setShowOfflineDialog] = useState(false);

  const handleOnlineInstall = () => {
    setShowProgressDialog(true);
  };

  const handleOfflineInstall = () => {
    setShowOfflineDialog(true);
  };

  const handleProgressDialogClose = () => {
    setShowProgressDialog(false);
    onClose(); // 关闭主对话框
  };

  const handleOfflineDialogClose = () => {
    setShowOfflineDialog(false);
  };

  return (
    <>
      <Modal
        title="安装必要组件"
        open={visible}
        onCancel={onClose}
        footer={null}
        width={500}
        centered
        maskClosable={false}
      >
        <Space direction="vertical" style={{ width: "100%" }}>
          <Title level={4}>需要安装必要组件</Title>
          <Paragraph>
            山竹阅卷需要安装必要组件才能正常运行，请选择安装方式：
          </Paragraph>

          <div style={{ marginTop: 16 }}>
            <Title level={5}>在线安装（推荐）</Title>
            <Paragraph>
              自动下载并安装最新版本的组件，支持实时进度显示。
            </Paragraph>
            <Button type="primary" onClick={handleOnlineInstall} style={{ marginBottom: 16 }}>
              在线安装
            </Button>
          </div>

          <div>
            <Title level={5}>离线安装</Title>
            <Paragraph>
              如果网络环境不佳，可以手动下载安装包进行离线安装。
            </Paragraph>
            <Button onClick={handleOfflineInstall}>
              离线安装
            </Button>
          </div>

          <div style={{ display: "flex", justifyContent: "flex-end", marginTop: 24 }}>
            <Button onClick={onClose}>
              稍后安装
            </Button>
          </div>
        </Space>
      </Modal>

      {/* 在线安装进度对话框 */}
      <InstallProgressDialog
        visible={showProgressDialog}
        onClose={handleProgressDialogClose}
      />

      {/* 离线安装对话框 */}
      <OfflineInstallDialog
        visible={showOfflineDialog}
        onClose={handleOfflineDialogClose}
      />
    </>
  );
};

export default PlaywrightInstallDialog;
