import React, { useEffect, useState } from "react";
import { Modal, Typography, Progress, Space, Button, Alert } from "antd";
import { InstallPlaywright } from "../../wailsjs/go/main/App";
import { LogInfo, LogError } from "../../wailsjs/runtime/runtime";

const { Paragraph, Text } = Typography;

interface InstallProgressDialogProps {
  visible: boolean;
  onClose: () => void;
}

interface DownloadProgress {
  downloaded: number;
  total: number;
  speed: number;
  percentage: number;
  fileName: string;
}

interface ExtractProgress {
  current: number;
  total: number;
  percentage: number;
  fileName: string;
}

interface OverallProgress {
  stage: string;
  percentage: number;
  message: string;
}

const InstallProgressDialog: React.FC<InstallProgressDialogProps> = ({
  visible,
  onClose,
}) => {
  const [installing, setInstalling] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress | null>(null);
  const [extractProgress, setExtractProgress] = useState<ExtractProgress | null>(null);
  const [overallProgress, setOverallProgress] = useState<OverallProgress | null>(null);
  const [currentMirror, setCurrentMirror] = useState<{ current: number; total: number; url: string } | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [completed, setCompleted] = useState(false);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 格式化下载速度
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + "/s";
  };

  useEffect(() => {
    if (!visible) {
      // 重置状态
      setInstalling(false);
      setDownloadProgress(null);
      setExtractProgress(null);
      setOverallProgress(null);
      setCurrentMirror(null);
      setErrorMessage("");
      setCompleted(false);
      return;
    }

    // 注册事件监听器
    const handleInstallStart = () => {
      LogInfo("安装开始");
      setInstalling(true);
      setCompleted(false);
      setErrorMessage("");
    };

    const handleDownloadProgress = () => {
      // @ts-ignore
      const progress = augments[0];
      setDownloadProgress(progress);
    };

    const handleExtractStart = () => {
      // @ts-ignore
      const totalFiles = augments[0];
      LogInfo(`开始解压，总文件数: ${totalFiles}`);
      setDownloadProgress(null); // 清除下载进度
    };

    const handleExtractProgress = () => {
      // @ts-ignore
      const progress = augments[0];
      setExtractProgress(progress);
    };

    const handleOverallProgress = () => {
      // @ts-ignore
      const progress = augments[0];
      setOverallProgress(progress);
      if (progress.stage === "complete") {
        setInstalling(false);
        setCompleted(true);
        setDownloadProgress(null);
        setExtractProgress(null);
        LogInfo("安装完成");
      }
    };

    const handleDownloadMirror = (mirror: { current: number; total: number; url: string }) => {
      setCurrentMirror(mirror);
      LogInfo(`尝试镜像 ${mirror.current}/${mirror.total}: ${mirror.url}`);
    };

    // 注册事件监听器
    window.runtime.EventsOn("install:start", handleInstallStart);
    window.runtime.EventsOn("download:progress", handleDownloadProgress);
    window.runtime.EventsOn("extract:start", handleExtractStart);
    window.runtime.EventsOn("extract:progress", handleExtractProgress);
    window.runtime.EventsOn("install:overall", handleOverallProgress);
    window.runtime.EventsOn("download:mirror", handleDownloadMirror as any);

    return () => {
      // 清理事件监听器
      window.runtime.EventsOff("install:start");
      window.runtime.EventsOff("download:progress");
      window.runtime.EventsOff("extract:start");
      window.runtime.EventsOff("extract:progress");
      window.runtime.EventsOff("install:overall");
      window.runtime.EventsOff("download:mirror");
    };
  }, [visible]);

  const handleInstall = async () => {
    try {
      setInstalling(true);
      setErrorMessage("");
      LogInfo("开始安装组件");
      await InstallPlaywright();
      LogInfo("组件安装成功");
    } catch (error) {
      const errorStr = String(error);
      LogError(`安装组件失败: ${errorStr}`);
      setErrorMessage(errorStr);
      setInstalling(false);
    }
  };

  const handleClose = () => {
    if (!installing) {
      onClose();
    }
  };

  return (
    <Modal
      title="安装必要组件"
      open={visible}
      onCancel={handleClose}
      footer={
        completed ? (
          <Button type="primary" onClick={onClose}>
            完成
          </Button>
        ) : installing ? (
          <Button disabled>安装中...</Button>
        ) : (
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={handleInstall}>
              开始安装
            </Button>
          </Space>
        )
      }
      closable={!installing}
      maskClosable={false}
      width={600}
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        {!installing && !completed && !errorMessage && (
          <>
            <Paragraph>
              系统需要下载并安装必要的组件才能正常运行，包括：
            </Paragraph>
            <ul>
              <li>驱动程序</li>
              <li>浏览器</li>
            </ul>
            <Alert
              message="注意"
              description="安装过程需要下载约100-200MB的文件，请确保网络连接正常。"
              type="info"
              showIcon
            />
          </>
        )}

        {errorMessage && (
          <Alert
            message="安装失败"
            description={errorMessage}
            type="error"
            showIcon
          />
        )}

        {completed && (
          <Alert
            message="安装成功"
            description="所有必要组件已成功安装，现在可以正常使用应用程序了。"
            type="success"
            showIcon
          />
        )}

        {installing && (
          <Space direction="vertical" style={{ width: "100%" }}>
            {/* 总体进度 */}
            {overallProgress && (
              <div>
                <Text strong>{overallProgress.message}</Text>
                <Progress
                  percent={Math.round(overallProgress.percentage)}
                  status={overallProgress.stage === "complete" ? "success" : "active"}
                />
              </div>
            )}

            {/* 当前镜像信息 */}
            {currentMirror && (
              <div>
                <Text type="secondary">
                  正在尝试镜像 {currentMirror.current}/{currentMirror.total}
                </Text>
              </div>
            )}

            {/* 下载进度 */}
            {downloadProgress && (
              <div>
                <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 4 }}>
                  <Text>下载: {downloadProgress.fileName}</Text>
                  <Text>
                    {formatFileSize(downloadProgress.downloaded)} / {formatFileSize(downloadProgress.total)}
                    {downloadProgress.speed > 0 && ` (${formatSpeed(downloadProgress.speed)})`}
                  </Text>
                </div>
                <Progress
                  percent={Math.round(downloadProgress.percentage)}
                  status="active"
                  size="small"
                />
              </div>
            )}

            {/* 解压进度 */}
            {extractProgress && (
              <div>
                <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 4 }}>
                  <Text>解压: {extractProgress.fileName}</Text>
                  <Text>
                    {extractProgress.current} / {extractProgress.total} 文件
                  </Text>
                </div>
                <Progress
                  percent={Math.round(extractProgress.percentage)}
                  status="active"
                  size="small"
                />
              </div>
            )}
          </Space>
        )}
      </Space>
    </Modal>
  );
};

export default InstallProgressDialog;
