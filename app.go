package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"maps"
	"math"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/adrg/xdg"
	"github.com/playwright-community/playwright-go"
	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

// App 是Wails应用的主要结构体，包含浏览器页面和上下文
type App struct {
	ctx                  context.Context    // Wails应用上下文
	pageTracker          *PageTracker       // 页面跟踪器
	loading              bool               // 加载状态
	autoGrading          bool               // 自动阅卷状态
	autoGradingDone      chan bool          // 自动阅卷完成通道，用于中断阅卷
	autoGradingCtx       context.Context    // 自动阅卷上下文
	autoGradingCancel    context.CancelFunc // 自动阅卷取消函数
	apiRequestInProgress bool               // API请求是否正在进行
	apiRequestMutex      sync.Mutex         // 保护API请求状态的互斥锁
	recentScreenshots    []string           // 存储最近的截图数据，用于检测重复
	sameScreenshotCount  int                // 连续相同截图的计数器
}

// NewApp 创建并返回一个新的App实例
func NewApp() *App {
	// 初始化全局map
	imageUrlMap = make(map[string]string)
	return &App{
		autoGrading:         false,
		autoGradingDone:     make(chan bool, 1),
		recentScreenshots:   make([]string, 0, 3), // 预分配容量为3的切片
		sameScreenshotCount: 0,
		pageTracker:         &PageTracker{},
	}
}

var (
	browserContext playwright.BrowserContext // 全局浏览器实例
	imageUrlMap    map[string]string         // 全局map，存储MH到图片URL的映射
)

const (
	GUANGDA     = "gdyj"
	APP_VERSION = "v0.1.0"
)

// GetVersion 获取版本信息
// 返回: 版本信息
func (a *App) GetVersion() string {
	return APP_VERSION
}

// GetTargetPage 获取当前页面
// 返回: 当前页面
func (a *App) GetTargetPage() playwright.Page {
	return a.pageTracker.GetTargetPage()
}

// InstallComponentsOffline 处理离线组件安装
// https://registry.npmmirror.com/-/binary/playwright/builds/chromium/1169/chromium-mac-arm64.zip
// 返回: 错误信息
func (a *App) InstallComponentsOffline() error {
	// 打开文件选择对话框
	filePath, err := rt.OpenFileDialog(a.ctx, rt.OpenDialogOptions{
		Title: "选择离线组件包",
		Filters: []rt.FileFilter{
			{DisplayName: "ZIP Files (*.zip)", Pattern: "*.zip"},
		},
	})
	if err != nil {
		return fmt.Errorf("打开文件选择对话框失败: %v", err)
	}
	if filePath == "" {
		return nil // 用户取消选择
	}
	var destPath string
	switch runtime.GOOS {
	case "windows":
		destPath = filepath.Join(xdg.Home, "AppData", "Local")
	case "darwin":
		destPath = xdg.CacheHome
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
	if err := UnzipFile(filePath, destPath); err != nil {
		return fmt.Errorf("解压文件失败: %v", err)
	}
	return nil
}

// startup 在应用启动时被Wails调用，保存上下文
// ctx: Wails应用上下文
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
}

// shutdown 在应用关闭时被Wails调用，清理资源
// ctx: Wails应用上下文
func (a *App) shutdown(ctx context.Context) {
	// 关闭浏览器实例
	if browserContext != nil {
		browserContext.Close()
	}

	// 发送事件通知前端保存评分标准
	if ctx != nil {
		rt.EventsEmit(ctx, "saveGradingCriteria")
	}
}

// ShowAbout 显示关于对话框
func (a *App) ShowAbout() {
	if a.ctx != nil {
		// 发送事件通知前端显示关于对话框
		rt.EventsEmit(a.ctx, "show:about")
	}
}

// GetGradingRecordsList 获取阅卷记录列表
// 返回: 阅卷记录查询结果
func (a *App) GetGradingRecordsList() GradingRecordsResult {
	db := GetDB()
	if db == nil {
		return GradingRecordsResult{
			Records: []GradingRecordListItem{},
			Total:   0,
			Error:   "数据库未初始化",
		}
	}
	// 调用record.go中的函数获取记录列表
	return GetGradingRecords(db)
}

// GetGradingRecordsListByTimeRange 根据时间范围获取阅卷记录列表
func (a *App) GetGradingRecordsListByTimeRange(startTime, endTime time.Time) GradingRecordsResult {
	db := GetDB()
	if db == nil {
		return GradingRecordsResult{
			Records: []GradingRecordListItem{},
			Total:   0,
			Error:   "数据库未初始化",
		}
	}
	// 调用record.go中的函数获取记录列表
	records := GetGradingRecordsByTimeRange(db, startTime, endTime)
	return GradingRecordsResult{
		Records: records,
		Total:   int64(len(records)),
		Error:   "",
	}
}

func (a *App) GetGradingRecordsListByTimeRangeAndCriteria(startTime, endTime time.Time, criteriaID string) GradingRecordsResult {
	db := GetDB()
	if db == nil {
		return GradingRecordsResult{
			Records: []GradingRecordListItem{},
			Total:   0,
			Error:   "数据库未初始化",
		}
	}
	// 调用record.go中的函数获取记录列表
	records := GetGradingRecordsByTimeRangeAndCriteria(db, criteriaID, startTime, endTime)
	return GradingRecordsResult{
		Records: records,
		Total:   int64(len(records)),
		Error:   "",
	}
}

// GetGradingCriteriaList 获取评分标准列表
func (a *App) GetGradingCriteriaList() []GradingCriteriaItem {
	db := GetDB()
	if db == nil {
		return []GradingCriteriaItem{}
	}
	// 调用record.go中的函数获取评分标准列表
	return GetGradingCriteriaList(db)
}

// DeleteGradingRecords 删除阅卷记录
// imageHashes: 要删除的记录的图片哈希值列表
// 返回: 删除结果，包含删除的记录数和可能的错误信息
func (a *App) DeleteGradingRecords(imageHashes []string) map[string]any {
	db := GetDB()
	if db == nil {
		return map[string]any{
			"count": 0,
			"error": "数据库未初始化",
		}
	}

	// 调用record.go中的函数删除记录
	count, err := DeleteGradingRecordsByImageHashes(db, imageHashes)
	if err != nil {
		return map[string]any{
			"count": 0,
			"error": err.Error(),
		}
	}

	return map[string]any{
		"count": count,
		"error": nil,
	}
}

// ShowGradingRecords 显示阅卷记录列表
func (a *App) ShowGradingRecords() {
	if a.ctx != nil {
		// 发送事件通知前端显示阅卷记录列表
		rt.EventsEmit(a.ctx, "show:gradingRecords")
	}
}

// StartLoading 通知前端开始加载
func (a *App) StartLoading() {
	a.loading = true
	// 发送加载开始事件到前端
	if a.ctx != nil {
		// 使用Wails运行时发送事件
		rt.EventsEmit(a.ctx, "loading:start")
	}
}

// StopLoading 通知前端结束加载
func (a *App) StopLoading() {
	a.loading = false
	// 发送加载结束事件到前端
	if a.ctx != nil {
		// 使用Wails运行时发送事件
		rt.EventsEmit(a.ctx, "loading:stop")
	}
}

// ShowPlaywrightInstallDialog 显示Playwright安装对话框
func (a *App) ShowPlaywrightInstallDialog() {
	if a.ctx != nil {
		// 发送事件通知前端显示安装对话框
		rt.EventsEmit(a.ctx, "show:playwrightInstall")
	}
}

// InstallPlaywright 安装Playwright浏览器
// 返回: 安装过程中遇到的错误
func (a *App) InstallPlaywright() error {
	if AppLogger != nil {
		AppLogger.Info("开始安装组件")
	}
	// 使用新的带进度的安装功能
	err := InstallPlaywrightWithProgress(a.ctx)
	if err != nil {
		if AppLogger != nil {
			AppLogger.Error("安装组件失败: %v", err)
		}
		return fmt.Errorf("安装组件失败: %v", err)
	}
	if AppLogger != nil {
		AppLogger.Info("组件安装成功")
	}
	return nil
}

// OpenURL 在浏览器中打开指定URL
// url: 要打开的网页地址
// 返回: 操作过程中遇到的错误
func (a *App) OpenURL(urlName string) error {
	if AppLogger != nil {
		AppLogger.Info("正在打开URL: %s", urlName)
	}
	var url string

	// 处理自定义URL选项
	if urlName == "custom" {
		// 对于自定义选项，打开一个空白页面
		url = "about:blank"
		if AppLogger != nil {
			AppLogger.Info("使用自定义选项，打开空白页面")
		}
	} else {
		// 否则从配置中查找URL
		for _, item := range GetGlobalConfig() {
			if urlName == item.ID {
				url = item.Url
				break
			}
		}
		if url == "" {
			return fmt.Errorf("未找到对应的URL")
		}
	}
	pw, err := playwright.Run()
	if err != nil {
		log.Println("could not start playwright:", err)
		if AppLogger != nil {
			AppLogger.Error("无法启动Playwright: %v", err)
		}
		a.ShowPlaywrightInstallDialog()
		return fmt.Errorf("请先到菜单中安装必要组件")
	}

	if AppLogger != nil {
		AppLogger.Info("正在启动浏览器")
	}
	if browserContext != nil {
		browserContext.Close()
	}
	option := playwright.BrowserTypeLaunchPersistentContextOptions{
		Headless: playwright.Bool(false), // 使用有界面模式
		Channel:  playwright.String("chromium"),
		Timeout:  playwright.Float(10000),
	}
	browserContext, err = pw.Chromium.LaunchPersistentContext(GetBrowserUserDataDir(), option)
	if err != nil {
		log.Println("could not launch browser:", err)
		if AppLogger != nil {
			AppLogger.Error("无法启动浏览器: %v", err)
		}
		a.ShowPlaywrightInstallDialog()
		return fmt.Errorf("请先到菜单中安装必要组件")
	}
	if AppLogger != nil {
		AppLogger.Info("浏览器已启动")
	}
	// 初始化当前页面
	pages := browserContext.Pages()
	if len(pages) > 0 {
		a.pageTracker.SetTargetPage(pages[0])
	} else {
		var newPage playwright.Page
		newPage, err = browserContext.NewPage()
		if err != nil {
			log.Println("could not create page:", err)
			if AppLogger != nil {
				AppLogger.Error("无法创建页面: %v", err)
			}
			return fmt.Errorf("无法打开页面")
		}
		a.pageTracker.SetTargetPage(newPage)
	}
	if AppLogger != nil {
		AppLogger.Info("正在导航到: %s", url)
	}
	// 增加响应监听器
	switch urlName {
	case GUANGDA:
		a.GetTargetPage().On("response", func(response playwright.Response) {
			specificUrl := response.URL()
			if strings.Contains(specificUrl, "/getDdb") || strings.Contains(specificUrl, "/getDdbByNext") {
				if AppLogger != nil {
					AppLogger.Info("捕获到响应: %s", "gdyj image")
				}
				go func(response playwright.Response) {
					// 获取响应体
					var body string
					body, err = response.Text()
					if err != nil {
						AppLogger.Error("读取响应体失败: %v", err)
						return
					}
					// 获取图片URL映射
					var urlMap map[string]string
					urlMap, err = getImageUrlMap([]byte(body))
					if err != nil {
						if AppLogger != nil {
							AppLogger.Error("获取图片URL映射失败: %v", err)
						}
						return
					}
					// 将获取到的映射添加到全局map中
					maps.Copy(imageUrlMap, urlMap)
				}(response)
			}
		})
	}
	// 导航到目标URL，等待DOM加载完成
	if _, err = a.GetTargetPage().Goto(url); err != nil {
		log.Println("could not navigate to URL:", err)
		if AppLogger != nil {
			AppLogger.Error("无法导航到URL: %v", err)
		}
		return fmt.Errorf("无法打开地址")
	}

	if AppLogger != nil {
		fmt.Println("Navigated to URL:", url)
		AppLogger.Info("成功打开URL: %s", url)
	}
	// 监听浏览器上下文事件
	browserContext.OnPage(func(page playwright.Page) {
		log.Println("检测到新页面创建")
		if a.ctx != nil {
			rt.EventsEmit(a.ctx, "autoGrading:error", "请在初始页面进行操作，不要在其他页面进行操作")
		}
	})
	// toast通知
	a.pageTracker.toastMessage()
	// 移除自动填充用户名和密码的功能
	return nil
}

// 移除fill方法，不再自动填充用户名和密码

// Login 执行登录操作
// username: 用户名
// password: 密码
// 返回: 操作过程中遇到的错误
func (a *App) Login(username string) error {
	if AppLogger != nil {
		AppLogger.Info("尝试登录用户: %s", username)
	}

	if a.GetTargetPage() == nil {
		log.Println("page not initialized")
		if AppLogger != nil {
			AppLogger.Error("页面未初始化")
		}
		return fmt.Errorf("page not initialized")
	}

	// 定位用户名和密码输入框
	if AppLogger != nil {
		AppLogger.Debug("定位输入元素")
	}

	// 点击登录按钮
	if AppLogger != nil {
		AppLogger.Info("点击登录按钮")
	}

	loginButton := a.GetTargetPage().Locator("button:has-text('登录')")
	if err := loginButton.Click(); err != nil {
		if AppLogger != nil {
			AppLogger.Error("无法点击登录按钮: %v", err)
		}
		return fmt.Errorf("failed to click login button: %v", err)
	}

	// TODO: 添加登录成功判断逻辑
	log.Println("Login successful")
	if AppLogger != nil {
		AppLogger.Info("登录成功: %s", username)
	}
	return nil
}

// func (a *App) GetUrlEnums() []string {
// 	keys := make([]string, 0, len(OriginURLs))
// 	for k := range OriginURLs {
// 		keys = append(keys, k)
// 	}
// 	return keys
// }

// 移除 ShowAuthDialog 方法

// GetAuth 获取存储的账号密码
func (a *App) GetAuth() (map[string]string, error) {
	username := GetConfig().GetString("username")
	encryptedPass := GetConfig().GetString("password")

	if username == "" && encryptedPass == "" {
		return nil, nil
	}

	// 解密密码
	var password string
	var err error
	if encryptedPass != "" {
		password, err = Decrypt(encryptedPass)
		if err != nil {
			return nil, fmt.Errorf("解密密码失败: %v", err)
		}
	}

	return map[string]string{
		"username": username,
		"password": password,
	}, nil
}

// SaveAuth 保存账号密码
func (a *App) SaveAuth(username, password string) error {
	// 加密密码并保存
	encryptedPass, err := Encrypt(password)
	if err != nil {
		return fmt.Errorf("加密密码失败: %v", err)
	}

	GetConfig().Set("username", username)
	GetConfig().Set("password", encryptedPass)

	// 保存到文件
	if err := GetConfig().WriteConfig(); err != nil {
		return fmt.Errorf("保存配置失败: %v", err)
	}

	return nil
}

func (a *App) GetAppAuth() (map[string]string, error) {
	username := GetConfig().GetString("app_username")
	encryptedPass := GetConfig().GetString("app_password")

	if username == "" && encryptedPass == "" {
		return nil, nil
	}

	// 解密密码
	var password string
	var err error
	if encryptedPass != "" {
		password, err = Decrypt(encryptedPass)
		if err != nil {
			return nil, fmt.Errorf("解密密码失败: %v", err)
		}
	}

	return map[string]string{
		"username": username,
		"password": password,
	}, nil
}

func (a *App) SaveAppAuth(username, password string) error {
	// 加密密码并保存
	encryptedPass, err := Encrypt(password)
	if err != nil {
		return fmt.Errorf("加密密码失败: %v", err)
	}

	GetConfig().Set("app_username", username)
	GetConfig().Set("app_password", encryptedPass)

	// 保存到文件
	if err := GetConfig().WriteConfig(); err != nil {
		return fmt.Errorf("保存配置失败: %v", err)
	}

	return nil
}

// UpdateMenuAfterLogin 登录成功后更新菜单
func (a *App) UpdateMenuAfterLogin() {
	if AppLogger != nil {
		AppLogger.Debug("登录成功，更新菜单")
	}

	// 更新为完整菜单（登录状态）
	UpdateAppMenu(a, true)
}

// UpdateMenuAfterLogout 登出后更新菜单
func (a *App) UpdateMenuAfterLogout() {
	if AppLogger != nil {
		AppLogger.Debug("登出成功，更新菜单")
	}

	// 更新为基本菜单（未登录状态）
	UpdateAppMenu(a, false)
}

// SaveGradingCriteriaStruct 保存结构化评分标准到配置文件
func (a *App) SaveGradingCriteriaStruct(criteria GradingCriteriaStruct, subject string) error {
	if AppLogger != nil {
		AppLogger.Info("保存结构化评分标准到配置文件")
	}
	// 保存结构化数据到配置文件
	GetConfig().Set("grading_criteria_struct", criteria)
	GetConfig().Set("selected_subject", subject)
	// 保存到文件
	if err := GetConfig().WriteConfig(); err != nil {
		return fmt.Errorf("保存评分标准失败: %v", err)
	}
	return nil
}

// GradeWithStructAndAnalysisMode 使用结构化评分标准和指定分析模式进行评分
func (a *App) GradeWithStructAndAnalysisMode(image string, criteria GradingCriteriaStruct, subject, analysisMode string) (*GradingResponse, error) {
	if AppLogger != nil {
		AppLogger.Info("使用结构化评分标准和分析模式进行评分，模式: %s", analysisMode)
	}
	// 调用支持分析模式的grade函数
	return gradeWithAnalysisMode(a.ctx, image, criteria, subject, analysisMode)
}

// GetGradingCriteriaStruct 从配置文件获取结构化评分标准
func (a *App) GetGradingCriteriaStruct() (map[string]any, error) {
	if AppLogger != nil {
		AppLogger.Info("从配置文件获取结构化评分标准")
	}
	// 尝试获取结构化评分标准
	var criteriaStruct GradingCriteriaStruct
	if err := GetConfig().UnmarshalKey("grading_criteria_struct", &criteriaStruct); err != nil {
		// 如果获取失败，返回空的结构化数据
		criteriaStruct = GradingCriteriaStruct{
			DefaultCriteria: "",
			ScoringPoints:   "",
			DeductionPoints: "",
			TotalScore:      0,
		}
	}
	subject := GetConfig().GetString("selected_subject")
	// 如果没有保存的学科，返回默认值
	if subject == "" {
		subject = "shs_geography" // 默认为地理
	}
	return map[string]any{
		"default_criteria": criteriaStruct.DefaultCriteria,
		"scoring_points":   criteriaStruct.ScoringPoints,
		"deduction_points": criteriaStruct.DeductionPoints,
		"total_score":      criteriaStruct.TotalScore,
		"subject":          subject,
	}, nil
}

// SelectExportDirectory 显示目录选择对话框，让用户选择导出目录
// 返回: 选择的目录路径，如果用户取消则返回空字符串
func (a *App) SelectExportDirectory() (string, error) {
	if a.ctx == nil {
		return "", fmt.Errorf("应用上下文未初始化")
	}

	// 显示目录选择对话框
	selectedDir, err := rt.OpenDirectoryDialog(a.ctx, rt.OpenDialogOptions{
		Title:                "选择导出目录",
		CanCreateDirectories: true,
	})
	if err != nil {
		if AppLogger != nil {
			AppLogger.Error("打开目录选择对话框失败: %v", err)
		}
		return "", fmt.Errorf("打开目录选择对话框失败: %v", err)
	}

	// 如果用户取消选择，返回空字符串
	if selectedDir == "" {
		return "", nil
	}

	// 保存选择的目录到配置文件，以便下次使用
	GetConfig().Set("export_directory", selectedDir)
	if err := GetConfig().WriteConfig(); err != nil {
		if AppLogger != nil {
			AppLogger.Error("保存导出目录到配置文件失败: %v", err)
		}
		// 不返回错误，因为这不是致命错误
	}

	return selectedDir, nil
}

// GetImageUrlMap 获取图片URL映射
// 返回: MH到图片URL的映射
func (a *App) GetImageUrlMap() map[string]string {
	// 返回一个副本，避免外部修改
	result := make(map[string]string)
	maps.Copy(result, imageUrlMap)
	return result
}

// GetDefaultExportDirectory 获取默认的导出目录
// 返回: 默认导出目录路径
func (a *App) GetDefaultExportDirectory() string {
	// 首先尝试从配置文件获取上次使用的导出目录
	exportDir := GetConfig().GetString("export_directory")
	if exportDir != "" {
		// 检查目录是否存在
		if _, err := os.Stat(exportDir); err == nil {
			return exportDir
		}
	}

	// 如果没有保存的导出目录或目录不存在，使用默认目录
	return filepath.Join(xdg.UserDirs.Documents, "山竹阅卷导出")
}

// SaveSelectedUrl 保存用户选择的URL到配置文件
// url: 用户选择的URL
// 返回: 错误信息
func (a *App) SaveSelectedUrl(url string) error {
	if AppLogger != nil {
		AppLogger.Info("保存用户选择的URL到配置文件: %s", url)
	}
	GetConfig().Set("selected_url", url)
	// 保存到文件
	if err := GetConfig().WriteConfig(); err != nil {
		return fmt.Errorf("保存URL失败: %v", err)
	}

	return nil
}

// GetSelectedUrl 从配置文件获取保存的URL
// 返回: 保存的URL，如果没有则返回空字符串
func (a *App) GetSelectedUrl() string {
	if AppLogger != nil {
		AppLogger.Info("从配置文件获取保存的URL")
	}
	return GetConfig().GetString("selected_url")
}

// AutoGradingLoopWithAnalysisMode 支持分析模式的自动阅卷循环
// id: 配置ID
// subject: 科目
// criteria: 评分标准
// count: 要阅卷的试卷数量
// analysisMode: 分析模式
// 返回: 错误信息，如果有的话
func (a *App) AutoGradingLoopWithAnalysisMode(id, subject string, criteria GradingCriteriaStruct, count int, analysisMode string) error {
	// 记录初始页面缩放比例
	var initialZoomLevel float64
	if a.GetTargetPage() != nil {
		zoomLevelJs := `window.devicePixelRatio`
		zoomLevelVal, err := a.GetTargetPage().Evaluate(zoomLevelJs)
		if err == nil && zoomLevelVal != nil {
			// 处理不同类型的返回值
			switch v := zoomLevelVal.(type) {
			case float64:
				initialZoomLevel = v
			case int:
				initialZoomLevel = float64(v)
			case int64:
				initialZoomLevel = float64(v)
			case float32:
				initialZoomLevel = float64(v)
			default:
				// 如果是其他类型，记录日志
				if AppLogger != nil {
					AppLogger.Warning("无法识别的初始缩放比例类型: %T", zoomLevelVal)
				}
				return fmt.Errorf("无法识别的初始缩放比例类型: %T", zoomLevelVal)
			}
			if AppLogger != nil {
				AppLogger.Debug("初始页面缩放比例: %.0f%%", initialZoomLevel*100)
			}
		}
	}
	// 如果已经在自动阅卷中，则停止当前的阅卷循环
	if a.autoGrading {
		if AppLogger != nil {
			AppLogger.Info("停止自动阅卷循环")
		}
		// 检查是否有API请求正在进行
		a.apiRequestMutex.Lock()
		if a.apiRequestInProgress {
			if AppLogger != nil {
				AppLogger.Debug("等待当前API请求完成")
			}
			// 发送事件通知前端正在等待API请求完成
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:waiting")
			}
			// 等待API请求完成
			for a.apiRequestInProgress {
				a.apiRequestMutex.Unlock()
				time.Sleep(100 * time.Millisecond)
				a.apiRequestMutex.Lock()
			}
		}
		a.apiRequestMutex.Unlock()
		// 发送信号停止当前的阅卷循环
		a.autoGradingDone <- true
		// 重置状态
		a.autoGrading = false
		// 发送事件通知前端自动阅卷已停止
		if a.ctx != nil {
			rt.EventsEmit(a.ctx, "autoGrading:stop")
		}
		return nil
	}
	// 检查参数
	if count <= 0 {
		return fmt.Errorf("试卷数量必须大于0")
	}
	// 设置自动阅卷状态
	a.autoGrading = true
	// 初始化通道
	a.autoGradingDone = make(chan bool, 1)
	// 创建新的上下文和取消函数
	a.autoGradingCtx, a.autoGradingCancel = context.WithCancel(context.Background())
	// 重置截图数据和计数器
	a.recentScreenshots = make([]string, 0, 3)
	a.sameScreenshotCount = 0
	// 发送事件通知前端自动阅卷已开始
	if a.ctx != nil {
		rt.EventsEmit(a.ctx, "autoGrading:start")
	}
	// 创建一个副本的通道，用于在协程中检测是否需要停止
	done := a.autoGradingDone
	// 在协程中运行阅卷循环
	go func() {
		defer func() {
			// 重置状态
			a.autoGrading = false
			// 发送事件通知前端自动阅卷已完成
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:complete")
			}
		}()
		if AppLogger != nil {
			AppLogger.Info("开始自动阅卷循环，试卷数量: %d", count)
		}
		// 初始化平均得分
		if a.ctx != nil {
			rt.EventsEmit(a.ctx, "autoGrading:averageScore", 0)
		}
		// 初始化得分列表
		var scores []int
		// 开始阅卷循环
		for i := range count {
			// 检查是否需要停止
			select {
			case <-done:
				if AppLogger != nil {
					AppLogger.Info("自动阅卷已停止，已完成 %d/%d 份试卷", i, count)
				}
				return
			default:
				// 继续执行
			}
			// 发送事件通知前端当前进度
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:progress", map[string]any{
					"current": i + 1,
					"total":   count,
				})
			}
			if AppLogger != nil {
				AppLogger.Info("正在阅卷 %d/%d", i+1, count)
			}
			// 检查页面缩放比例是否变化
			if a.GetTargetPage() != nil && initialZoomLevel > 0 {
				zoomLevelJs := `window.devicePixelRatio`
				zoomLevelVal, err := a.GetTargetPage().Evaluate(zoomLevelJs)
				if err == nil && zoomLevelVal != nil {
					// 处理不同类型的返回值
					var currentZoomLevel float64
					switch v := zoomLevelVal.(type) {
					case float64:
						currentZoomLevel = v
					case int:
						currentZoomLevel = float64(v)
					case int64:
						currentZoomLevel = float64(v)
					case float32:
						currentZoomLevel = float64(v)
					default:
						// 如果是其他类型，记录日志并跳过检查
						if AppLogger != nil {
							AppLogger.Warning("无法识别的缩放比例类型: %T", zoomLevelVal)
						}
						continue
					}
					if AppLogger != nil {
						AppLogger.Debug("当前页面缩放比例: %.0f%%", currentZoomLevel*100)
					}
					// 检查缩放比例是否变化
					if math.Abs(currentZoomLevel-initialZoomLevel) > 0.01 { // 允许0.01的误差
						if AppLogger != nil {
							AppLogger.Warning("页面缩放比例已变化，从 %.0f%% 变为 %.0f%%，停止阅卷", initialZoomLevel*100, currentZoomLevel*100)
						}
						// 弹出对话框提醒用户
						if a.ctx != nil {
							rt.EventsEmit(a.ctx, "autoGrading:error", "检测到页面缩放比例已变化！\n\n原始比例："+fmt.Sprintf("%.0f%%", initialZoomLevel*100)+"\n当前比例："+fmt.Sprintf("%.0f%%", currentZoomLevel*100)+"\n\n请恢复原始缩放比例后重新开始阅卷。\n提示：可以使用Ctrl+0重置浏览器缩放比例。")
						}
						a.apiRequestMutex.Lock()
						a.apiRequestInProgress = false
						a.apiRequestMutex.Unlock()
						return
					}
				}
			}

			// 执行自动阅卷
			a.apiRequestMutex.Lock()
			a.apiRequestInProgress = true
			a.apiRequestMutex.Unlock()

			response, err := a.AutoGradeWithAnalysisMode(id, subject, criteria, analysisMode)

			a.apiRequestMutex.Lock()
			a.apiRequestInProgress = false
			a.apiRequestMutex.Unlock()

			if err != nil {
				if AppLogger != nil {
					AppLogger.Error("自动阅卷失败: %v", err)
				}
				// 错误已经通过事件发送给前端
				// 发送事件通知前端阅卷出错
				if a.ctx != nil {
					rt.EventsEmit(a.ctx, "autoGrading:error", err.Error())
				}
				// 出错后停止阅卷循环
				if AppLogger != nil {
					AppLogger.Info("因错误停止自动阅卷循环，已完成 %d/%d 份试卷", i, count)
				}
				return
			}
			if response != nil {
				scores = append(scores, response.Score)
				averageScore := CalculateAverageScore(scores)
				if a.ctx != nil {
					rt.EventsEmit(a.ctx, "autoGrading:averageScore", averageScore)
				}
			}
			// 评分结果已经通过事件发送给前端
			// 发送事件通知前端当前评分结果
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:result", response)
			}
			// 等待一小段时间，避免过快请求
			select {
			case <-done:
				return
			// TODO：自定义等待时间
			case <-time.After(2 * time.Second):
				// 继续执行
			}
		}

		if AppLogger != nil {
			AppLogger.Info("自动阅卷循环完成，共完成 %d 份试卷", count)
		}
	}()

	// 返回成功状态，表示自动阅卷已开始
	// 注意：这里的返回值不会被前端使用，因为自动阅卷是异步进行的
	return nil
}

// AutoGradingLoopCustomWithAnalysisMode 支持分析模式的自定义自动阅卷循环
// subject: 科目
// criteria: 评分标准
// count: 要阅卷的试卷数量
// areaCoords: 区域坐标信息，包含x, y, width, height
// elementsJson: 元素操作的JSON字符串
// analysisMode: 分析模式
// 返回: 错误信息，如果有的话
func (a *App) AutoGradingLoopCustomWithAnalysisMode(subject string, criteria GradingCriteriaStruct, count int, areaCoords map[string]int, elementsJson string, analysisMode string) error {
	// 记录初始页面缩放比例
	var initialZoomLevel float64
	if a.GetTargetPage() != nil {
		zoomLevelJs := `window.devicePixelRatio`
		zoomLevelVal, err := a.GetTargetPage().Evaluate(zoomLevelJs)
		if err == nil && zoomLevelVal != nil {
			// 处理不同类型的返回值
			switch v := zoomLevelVal.(type) {
			case float64:
				initialZoomLevel = v
			case int:
				initialZoomLevel = float64(v)
			case int64:
				initialZoomLevel = float64(v)
			case float32:
				initialZoomLevel = float64(v)
			default:
				// 如果是其他类型，记录日志
				if AppLogger != nil {
					AppLogger.Warning("无法识别的初始缩放比例类型: %T", zoomLevelVal)
				}
				return fmt.Errorf("无法识别的初始缩放比例类型: %T", zoomLevelVal)
			}
			if AppLogger != nil {
				AppLogger.Debug("初始页面缩放比例: %.0f%%", initialZoomLevel*100)
			}
		}
	}
	// 如果已经在自动阅卷中，则停止当前的阅卷循环
	if a.autoGrading {
		if AppLogger != nil {
			AppLogger.Info("停止自定义自动阅卷循环")
		}
		// 检查是否有API请求正在进行
		a.apiRequestMutex.Lock()
		if a.apiRequestInProgress {
			if AppLogger != nil {
				AppLogger.Debug("等待当前API请求完成")
			}
			// 发送事件通知前端正在等待API请求完成
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:waiting")
			}
			// 等待API请求完成
			for a.apiRequestInProgress {
				a.apiRequestMutex.Unlock()
				time.Sleep(100 * time.Millisecond)
				a.apiRequestMutex.Lock()
			}
		}
		a.apiRequestMutex.Unlock()
		// 发送信号停止当前的阅卷循环
		a.autoGradingDone <- true
		// 重置状态
		a.autoGrading = false
		// 发送事件通知前端自动阅卷已停止
		if a.ctx != nil {
			rt.EventsEmit(a.ctx, "autoGrading:stop")
		}
		return nil
	}
	// 检查参数
	if count <= 0 {
		return fmt.Errorf("试卷数量必须大于0")
	}
	// 验证区域坐标
	if areaCoords == nil || areaCoords["width"] <= 0 || areaCoords["height"] <= 0 {
		return fmt.Errorf("无效的区域坐标")
	}
	// 解析元素操作
	var elements []ElementOperation
	if err := json.Unmarshal([]byte(elementsJson), &elements); err != nil {
		return fmt.Errorf("解析元素操作失败: %v", err)
	}
	// 验证元素操作
	if len(elements) == 0 {
		return fmt.Errorf("未设置元素操作")
	}
	// 检查是否有输入操作
	hasInputOperation := false
	for _, element := range elements {
		if element.Type == "input" {
			hasInputOperation = true
			break
		}
	}
	if !hasInputOperation {
		return fmt.Errorf("元素操作中必须包含至少一个输入操作")
	}
	// 设置自动阅卷状态
	a.autoGrading = true
	// 初始化通道
	a.autoGradingDone = make(chan bool, 1)
	// 创建新的上下文和取消函数
	a.autoGradingCtx, a.autoGradingCancel = context.WithCancel(context.Background())
	// 重置截图数据和计数器
	a.recentScreenshots = make([]string, 0, 3)
	a.sameScreenshotCount = 0
	// 发送事件通知前端自动阅卷已开始
	if a.ctx != nil {
		rt.EventsEmit(a.ctx, "autoGrading:start")
	}
	// 创建一个副本的通道，用于在协程中检测是否需要停止
	done := a.autoGradingDone
	// 在协程中运行阅卷循环
	go func() {
		defer func() {
			// 重置状态
			a.autoGrading = false
			// 发送事件通知前端自动阅卷已完成
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:complete")
			}
		}()
		if AppLogger != nil {
			AppLogger.Info("开始自定义自动阅卷循环，试卷数量: %d", count)
		}
		// 初始化平均得分
		if a.ctx != nil {
			rt.EventsEmit(a.ctx, "autoGrading:averageScore", 0)
		}
		// 初始化得分列表
		var scores []int
		// 开始阅卷循环
		for i := range count {
			// 检查是否需要停止
			select {
			case <-done:
				if AppLogger != nil {
					AppLogger.Info("自定义自动阅卷已停止，已完成 %d/%d 份试卷", i, count)
				}
				return
			default:
				// 继续执行
			}
			// 发送事件通知前端当前进度
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:progress", map[string]any{
					"current": i + 1,
					"total":   count,
				})
			}
			if AppLogger != nil {
				AppLogger.Info("正在阅卷 %d/%d", i+1, count)
			}
			// 检查页面缩放比例是否变化
			if a.GetTargetPage() != nil && initialZoomLevel > 0 {
				zoomLevelJs := `window.devicePixelRatio`
				zoomLevelVal, err := a.GetTargetPage().Evaluate(zoomLevelJs)
				if err == nil && zoomLevelVal != nil {
					// 处理不同类型的返回值
					var currentZoomLevel float64
					switch v := zoomLevelVal.(type) {
					case float64:
						currentZoomLevel = v
					case int:
						currentZoomLevel = float64(v)
					case int64:
						currentZoomLevel = float64(v)
					case float32:
						currentZoomLevel = float64(v)
					default:
						// 如果是其他类型，记录日志并跳过检查
						if AppLogger != nil {
							AppLogger.Warning("无法识别的缩放比例类型: %T", zoomLevelVal)
						}
						continue
					}
					if AppLogger != nil {
						AppLogger.Debug("当前页面缩放比例: %.0f%%", currentZoomLevel*100)
					}
					// 检查缩放比例是否变化
					if math.Abs(currentZoomLevel-initialZoomLevel) > 0.01 { // 允许0.01的误差
						if AppLogger != nil {
							AppLogger.Warning("页面缩放比例已变化，从 %.0f%% 变为 %.0f%%，停止阅卷", initialZoomLevel*100, currentZoomLevel*100)
						}
						// 弹出对话框提醒用户
						if a.ctx != nil {
							rt.EventsEmit(a.ctx, "autoGrading:error", "检测到页面缩放比例已变化！\n\n原始比例："+fmt.Sprintf("%.0f%%", initialZoomLevel*100)+"\n当前比例："+fmt.Sprintf("%.0f%%", currentZoomLevel*100)+"\n\n请恢复原始缩放比例后重新开始阅卷。\n提示：可以使用Ctrl+0重置浏览器缩放比例。")
						}
						a.apiRequestMutex.Lock()
						a.apiRequestInProgress = false
						a.apiRequestMutex.Unlock()
						return
					}
				}
			}

			// 执行自定义自动阅卷
			a.apiRequestMutex.Lock()
			a.apiRequestInProgress = true
			a.apiRequestMutex.Unlock()
			// 1. 截取指定区域的截图
			if AppLogger != nil {
				AppLogger.Debug("截取指定区域: x=%d, y=%d, width=%d, height=%d",
					areaCoords["x"], areaCoords["y"], areaCoords["width"], areaCoords["height"])
			}
			screenshotData, err := a.ScreenshotArea(
				areaCoords["x"],
				areaCoords["y"],
				areaCoords["width"],
				areaCoords["height"],
				800) // 最大宽度设为800
			if err != nil {
				if AppLogger != nil {
					AppLogger.Error("截图失败: %v", err)
				}
				// 发送事件通知前端阅卷出错
				if a.ctx != nil {
					rt.EventsEmit(a.ctx, "autoGrading:error", fmt.Sprintf("截图失败: %v", err))
				}
				a.apiRequestMutex.Lock()
				a.apiRequestInProgress = false
				a.apiRequestMutex.Unlock()
				return
			}

			// 检测连续相同的截图数据
			// 检查当前截图是否与最近的截图相同
			if len(a.recentScreenshots) > 0 && screenshotData == a.recentScreenshots[len(a.recentScreenshots)-1] {
				a.sameScreenshotCount++
				if AppLogger != nil {
					AppLogger.Warning("检测到相同的截图数据，连续次数: %d", a.sameScreenshotCount)
				}
				// 如果连续三次相同，停止阅卷并提示用户
				if a.sameScreenshotCount >= 3 {
					if AppLogger != nil {
						AppLogger.Warning("检测到连续三次相同的截图数据，停止阅卷")
					}
					// 弹出对话框提醒用户
					if a.ctx != nil {
						rt.EventsEmit(a.ctx, "autoGrading:error", "检测到重复阅卷，请检查配置是否正确，例如没有选择提交元素等")
					}
					a.apiRequestMutex.Lock()
					a.apiRequestInProgress = false
					a.apiRequestMutex.Unlock()
					return
				}
			} else {
				// 不相同，重置计数器
				a.sameScreenshotCount = 1
			}

			// 更新最近的截图数据
			if len(a.recentScreenshots) >= 3 {
				// 保留最近的两张截图
				a.recentScreenshots = a.recentScreenshots[1:]
			}
			a.recentScreenshots = append(a.recentScreenshots, screenshotData)
			// 发送截图数据到前端
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:fetchimage", screenshotData)
			}
			// 2. 调用API进行评分
			if AppLogger != nil {
				AppLogger.Debug("开始调用AI进行评分")
			}
			gradingResponse, err := gradeWithAnalysisMode(a.ctx, screenshotData, criteria, subject, analysisMode)
			if err != nil {
				if AppLogger != nil {
					AppLogger.Error("评分失败: %v", err)
				}
				// 发送事件通知前端阅卷出错
				if a.ctx != nil {
					rt.EventsEmit(a.ctx, "autoGrading:error", fmt.Sprintf("评分失败: %v", err))
				}
				a.apiRequestMutex.Lock()
				a.apiRequestInProgress = false
				a.apiRequestMutex.Unlock()
				return
			}
			if gradingResponse != nil {
				scores = append(scores, gradingResponse.Score)
				averageScore := CalculateAverageScore(scores)
				if a.ctx != nil {
					rt.EventsEmit(a.ctx, "autoGrading:averageScore", averageScore)
				}
			}
			if AppLogger != nil {
				AppLogger.Info("评分完成，分数: %d", gradingResponse.Score)
			}
			// 3. 执行元素操作
			// 更新输入操作的值为评分结果
			elementsToExecute := make([]ElementOperation, len(elements))
			copy(elementsToExecute, elements)

			for i, element := range elementsToExecute {
				if element.Type == "input" {
					elementsToExecute[i].Value = fmt.Sprintf("%d", gradingResponse.Score)
				}
			}
			// 将元素操作转换为JSON
			operationsJson, err := json.Marshal(elementsToExecute)
			if err != nil {
				if AppLogger != nil {
					AppLogger.Error("序列化元素操作失败: %v", err)
				}
				// 发送事件通知前端阅卷出错
				if a.ctx != nil {
					rt.EventsEmit(a.ctx, "autoGrading:error", fmt.Sprintf("序列化元素操作失败: %v", err))
				}
				a.apiRequestMutex.Lock()
				a.apiRequestInProgress = false
				a.apiRequestMutex.Unlock()
				return
			}
			// 执行元素操作
			if AppLogger != nil {
				AppLogger.Debug("开始执行元素操作")
			}
			err = a.ExecuteElementOperations(string(operationsJson))
			if err != nil {
				if AppLogger != nil {
					AppLogger.Error("执行元素操作失败: %v", err)
				}
				// 发送事件通知前端阅卷出错
				if a.ctx != nil {
					rt.EventsEmit(a.ctx, "autoGrading:error", fmt.Sprintf("执行元素操作失败: %v", err))
				}
				a.apiRequestMutex.Lock()
				a.apiRequestInProgress = false
				a.apiRequestMutex.Unlock()
				return
			}
			a.apiRequestMutex.Lock()
			a.apiRequestInProgress = false
			a.apiRequestMutex.Unlock()
			// 发送事件通知前端当前评分结果
			if a.ctx != nil {
				rt.EventsEmit(a.ctx, "autoGrading:result", gradingResponse)
			}
			// 等待一小段时间，避免过快请求
			select {
			case <-done:
				return
			// TODO：自定义等待时间
			case <-time.After(2 * time.Second):
				// 继续执行
			}
		}
		if AppLogger != nil {
			AppLogger.Info("自定义自动阅卷循环完成，共完成 %d 份试卷", count)
		}
	}()
	// 返回成功状态，表示自动阅卷已开始
	return nil
}

// AutoGrade 执行自动阅卷操作，根据Actions中index的顺序执行操作，根据type来执行element_actions中的操作
// id: 配置ID
// subject: 科目
// prompt: 评分标准
// ctx: 上下文，用于取消操作
// 返回: (评分结果, 错误)
func (a *App) AutoGrade(id, subject string, criteria GradingCriteriaStruct) (*GradingResponse, error) {
	// 使用默认分析模式
	return a.AutoGradeWithAnalysisMode(id, subject, criteria, "standard")
}

// AutoGradeWithAnalysisMode 支持分析模式的自动阅卷操作
// id: 配置ID
// subject: 科目
// criteria: 评分标准
// analysisMode: 分析模式
// 返回: (评分结果, 错误)
func (a *App) AutoGradeWithAnalysisMode(id, subject string, criteria GradingCriteriaStruct, analysisMode string) (*GradingResponse, error) {
	if AppLogger != nil {
		AppLogger.Info("开始自动阅卷操作,配置ID: %s", id)
	}
	if a.GetTargetPage() == nil {
		return nil, fmt.Errorf("页面未初始化,请先打开URL")
	}
	// 获取配置
	config := GetGlobalConfig()
	var targetConfig *ConfigItem
	for _, item := range config {
		if item.ID == id {
			targetConfig = &item
			break
		}
	}
	if targetConfig == nil {
		return nil, fmt.Errorf("未找到配置ID: %s", id)
	}
	if len(targetConfig.Actions) == 0 {
		return nil, fmt.Errorf("配置ID %s 没有定义任何操作", id)
	}
	// 按照Index排序Actions
	sortedActions := make([]Action, len(targetConfig.Actions))
	copy(sortedActions, targetConfig.Actions)
	// 使用冒泡排序按Index排序
	for i := range sortedActions[:len(sortedActions)-1] {
		for j := range sortedActions[:len(sortedActions)-i-1] {
			if sortedActions[j].Index > sortedActions[j+1].Index {
				sortedActions[j], sortedActions[j+1] = sortedActions[j+1], sortedActions[j]
			}
		}
	}
	// 执行每个操作
	var gradingResponse *GradingResponse
	// 首先遍历所有操作，找到截图操作并执行
	for _, action := range sortedActions {
		// 检查是否已取消
		select {
		case <-a.autoGradingCtx.Done():
			return nil, fmt.Errorf("操作已取消")
		default:
			// 继续执行
		}
		if action.Type == "screenshot" {
			if AppLogger != nil {
				AppLogger.Debug("执行截图操作: %s", action.Selector)
			}
			// 默认最大宽度为800，可以根据需要调整
			maxWidth := 800
			var err error
			screenshotData, err := a.ScreenshotElement(action.Selector, maxWidth)
			if err != nil {
				return nil, fmt.Errorf("截图失败: %v", err)
			}
			rt.EventsEmit(a.ctx, "autoGrading:fetchimage", screenshotData)
			// 获取截图后立即调用API进行评分
			if AppLogger != nil {
				AppLogger.Debug("开始调用AI进行评分")
			}
			gradingResponse, err = gradeWithAnalysisMode(a.ctx, screenshotData, criteria, subject, analysisMode)
			if err != nil {
				return nil, fmt.Errorf("评分失败: %v", err)
			}
			if AppLogger != nil {
				AppLogger.Info("评分完成，分数: %d", gradingResponse.Score)
			}
			break
		} else if action.Type == "fetch" && action.Value == id {
			// 光大阅卷
			var err error
			screenshotData, err := a.FetchImage(action.Value)
			if err != nil {
				return nil, fmt.Errorf("抓取失败: %v", err)
			}
			rt.EventsEmit(a.ctx, "autoGrading:fetchimage", screenshotData)
			// 获取图片后立即调用API进行评分
			if AppLogger != nil {
				AppLogger.Debug("开始调用AI进行评分")
			}
			gradingResponse, err = gradeWithAnalysisMode(a.ctx, screenshotData, criteria, subject, analysisMode)
			if err != nil {
				return nil, fmt.Errorf("评分失败: %v", err)
			}
			if AppLogger != nil {
				AppLogger.Info("评分完成，分数: %d", gradingResponse.Score)
			}
			break
		}
	}
	// 如果没有评分结果，无法进行评分
	if gradingResponse == nil {
		return nil, fmt.Errorf("未获取到截图数据或评分结果,请确保配置中包含screenshot类型的操作")
	}
	// 执行其他操作
	for _, action := range sortedActions {
		// 检查是否已取消
		select {
		case <-a.autoGradingCtx.Done():
			return nil, fmt.Errorf("操作已取消")
		default:
			// 继续执行
		}
		// 跳过截图操作，因为已经处理过了
		if action.Type == "screenshot" || action.Type == "fetch" {
			continue
		}
		if AppLogger != nil {
			AppLogger.Debug("执行操作 %d: %s %s", action.Index, action.Type, action.Selector)
		}
		switch action.Type {
		case "click":
			if err := a.ClickElement(action.Selector); err != nil {
				return nil, fmt.Errorf("点击元素失败: %v", err)
			}
		case "fill":
			if err := a.FillElement(action.Selector, fmt.Sprintf("%d", gradingResponse.Score)); err != nil {
				return nil, fmt.Errorf("填充元素失败: %v", err)
			}
		default:
			if AppLogger != nil {
				AppLogger.Warning("未知操作类型: %s，跳过", action.Type)
			}
		}
	}
	if AppLogger != nil {
		AppLogger.Info("评分完成，分数: %d", gradingResponse.Score)
	}
	return gradingResponse, nil
}

// gradeWithAnalysisMode 使用指定分析模式进行评分
func gradeWithAnalysisMode(ctx context.Context, image string, criteria GradingCriteriaStruct, subject, analysisMode string) (*GradingResponse, error) {
	fmt.Printf("subject: %v\n", subject)
	fmt.Printf("prompt: %+v\n", criteria.ToPromptText())
	fmt.Printf("analysisMode: %v\n", analysisMode)
	log.Print("正在评分")
	// 添加总体计时
	startTime := time.Now()
	// 检查是否已经有相同图片的评分记录，仅用于日志记录
	_, found, err := FindGradingRecordByImage(GetDB(), image)
	if err != nil {
		log.Printf("查询评分记录时出错: %v", err)
		// 继续处理，不中断流程
	} else if found {
		// 找到了相同图片的记录，但仍然调用API重新评分并更新记录
		log.Print("找到相同图片的评分记录，将重新评分并更新")
	}
	// 调用API进行评分之前，先上传图片到TOS
	tosStartTime := time.Now()
	// 上传图片到TOS
	imageUrl, err := UploadImageToTOS(ctx, image)
	if err != nil {
		log.Printf("上传图片到TOS失败: %v", err)
		return nil, err
	}
	tosEndTime := time.Now()
	tosDuration := tosEndTime.Sub(tosStartTime)
	log.Printf("上传图片到TOS耗时: %v", tosDuration)
	// 检查腾讯云客户端是否初始化
	if TencentCloudHttpClient == nil {
		return nil, fmt.Errorf("腾讯云客户端未初始化")
	}
	// 添加API调用计时
	apiStartTime := time.Now()
	// 调用API进行评分，使用指定的分析模式
	data, err := TencentCloudHttpClient.ChatWithAnalysisMode(ctx, imageUrl, criteria.ToPromptText(), subject, analysisMode)
	apiEndTime := time.Now()
	apiDuration := apiEndTime.Sub(apiStartTime)
	log.Printf("API调用耗时: %v", apiDuration)
	if err != nil {
		return nil, err
	}
	log.Print("评分完成")
	gradingResponse := &GradingResponse{}
	// 将OCR结果添加到评分响应中
	gradingResponse.StudentAnswer = data.Analysis.StudentAnswer
	gradingResponse.Score = data.Analysis.Score
	gradingResponse.GradingDetails = data.Analysis.GradingDetails
	// 评分细节在分号和句号后分行
	gradingResponse.GradingDetails = strings.ReplaceAll(gradingResponse.GradingDetails, ";", ";\n")
	gradingResponse.GradingDetails = strings.ReplaceAll(gradingResponse.GradingDetails, "；", "；\n")
	gradingResponse.GradingDetails = strings.ReplaceAll(gradingResponse.GradingDetails, "。", "。\n")
	// 保存评分记录到数据库
	go func() {
		// 获取用户邮箱
		userEmail := GetConfig().GetString("app_username")
		if userEmail == "" {
			userEmail = "anonymous"
		}
		// 序列化评分标准
		var criteriaBytes []byte
		criteriaBytes, _ = json.Marshal(criteria)
		criteriaString := string(criteriaBytes)
		// 保存记录
		err := SaveGradingRecord(
			GetDB(),
			userEmail,
			image,
			criteriaString,
			gradingResponse.StudentAnswer,
			float64(gradingResponse.Score),
			gradingResponse.GradingDetails,
		)
		if err != nil {
			log.Printf("保存评分记录失败: %v", err)
			if AppLogger != nil {
				AppLogger.Error("保存评分记录失败: %v", err)
			}
		}
	}()
	// 记录总耗时
	endTime := time.Now()
	totalDuration := endTime.Sub(startTime)
	log.Printf("Grade函数总耗时: %v", totalDuration)
	return gradingResponse, nil
}

// DataSize 表示数据大小信息
type DataSize struct {
	Path string `json:"path"`
	Size int64  `json:"size"`
	Name string `json:"name"`
}

// AppDataInfo 表示应用数据信息
type AppDataInfo struct {
	Logs       DataSize `json:"logs"`
	Browser    DataSize `json:"browser"`
	Config     DataSize `json:"config"`
	Database   DataSize `json:"database"`
	Components DataSize `json:"components"`
	TotalSize  int64    `json:"totalSize"`
}

// GetAppDataInfo 获取应用数据信息
func (a *App) GetAppDataInfo() AppDataInfo {
	// 获取应用数据目录
	appDir := filepath.Join(xdg.ConfigHome, "ai-grading")

	// 获取日志目录大小
	logsDir := filepath.Join(appDir, "logs")
	logsSize := getDirSize(logsDir)

	// 获取浏览器数据目录大小
	browserDir := GetBrowserUserDataDir()
	browserSize := getDirSize(browserDir)

	// 获取配置文件大小
	configPath := filepath.Join(appDir, "config.yaml")
	configSize := getFileSize(configPath)

	// 获取数据库文件大小
	dbPath := GetDBPath()
	dbSize := getFileSize(dbPath)

	// 获取组件文件大小
	// 根据操作系统确定组件目录
	var componentsDir string
	switch runtime.GOOS {
	case "windows":
		componentsDir = filepath.Join(xdg.Home, "AppData", "Local")
	case "darwin":
		componentsDir = xdg.CacheHome
	}
	// 计算ms-playwright和ms-playwright-go目录的大小
	playwrightDir := filepath.Join(componentsDir, "ms-playwright")
	playwrightGoDir := filepath.Join(componentsDir, "ms-playwright-go")
	playwrightSize := getDirSize(playwrightDir)
	playwrightGoSize := getDirSize(playwrightGoDir)
	componentsSize := playwrightSize + playwrightGoSize
	// 计算总大小
	totalSize := logsSize + browserSize + configSize + dbSize + componentsSize

	return AppDataInfo{
		Logs: DataSize{
			Path: logsDir,
			Size: logsSize,
			Name: "日志文件",
		},
		Browser: DataSize{
			Path: browserDir,
			Size: browserSize,
			Name: "浏览器数据",
		},
		Config: DataSize{
			Path: configPath,
			Size: configSize,
			Name: "配置文件",
		},
		Database: DataSize{
			Path: dbPath,
			Size: dbSize,
			Name: "阅卷记录数据库",
		},
		Components: DataSize{
			Path: componentsDir,
			Size: componentsSize,
			Name: "组件文件",
		},
		TotalSize: totalSize,
	}
}

// ClearAppData 清除应用数据
// dataTypes: 要清除的数据类型列表，可以是 "logs", "browser", "config", "database"
// 返回: 清除结果，包含清除的数据类型和可能的错误信息
func (a *App) ClearAppData(dataTypes []string) map[string]any {
	results := make(map[string]any)
	errors := make([]string, 0)

	for _, dataType := range dataTypes {
		var err error
		switch dataType {
		case "logs":
			// 清除日志文件
			logsDir := filepath.Join(xdg.ConfigHome, "ai-grading", "logs")
			err = clearDirectory(logsDir)
		case "browser":
			// 清除浏览器数据
			// 先关闭浏览器实例
			if browserContext != nil {
				browserContext.Close()
				browserContext = nil
			}
			// 清除浏览器数据目录
			err = clearDirectory(GetBrowserUserDataDir())
		case "config":
			// 清除配置文件
			configPath := filepath.Join(xdg.ConfigHome, "ai-grading", "config.yaml")
			err = os.Remove(configPath)
			// 重新初始化配置
			initConfig()
		case "database":
			// 关闭数据库连接
			if DB != nil {
				db, _ := DB.DB()
				if db != nil {
					db.Close()
				}
				DB = nil
			}
			// 删除数据库文件
			dbPath := GetDBPath()
			err = os.Remove(dbPath)
			// 重新初始化数据库
			_, _ = InitDB()
		case "components":
			// 清除组件文件
			// 根据操作系统确定组件目录
			var componentsDir string
			switch runtime.GOOS {
			case "windows":
				componentsDir = filepath.Join(xdg.Home, "AppData", "Local")
			case "darwin":
				componentsDir = xdg.CacheHome
			}
			// 关闭浏览器实例，因为它可能依赖于这些组件
			if browserContext != nil {
				browserContext.Close()
				browserContext = nil
			}
			// 删除ms-playwright目录
			playwrightDir := filepath.Join(componentsDir, "ms-playwright")
			if _, err = os.Stat(playwrightDir); !os.IsNotExist(err) {
				err = os.RemoveAll(playwrightDir)
				if err != nil {
					errors = append(errors, fmt.Sprintf("删除ms-playwright目录失败: %v", err))
				}
			}
			// 删除ms-playwright-go目录
			playwrightGoDir := filepath.Join(componentsDir, "ms-playwright-go")
			if _, err = os.Stat(playwrightGoDir); !os.IsNotExist(err) {
				err = os.RemoveAll(playwrightGoDir)
				if err != nil {
					errors = append(errors, "部分组件删除失败，请尝试重新打开软件，在不登录的情况下删除")
				}
			}
		default:
			err = fmt.Errorf("未知的数据类型: %s", dataType)
		}
		if err != nil && dataType != "components" {
			errors = append(errors, fmt.Sprintf("%s: %v", dataType, err))
		}
	}

	// 获取更新后的数据信息
	updatedInfo := a.GetAppDataInfo()

	results["dataInfo"] = updatedInfo
	if len(errors) > 0 {
		results["errors"] = errors
	}

	return results
}

// ShowClearDataDialog 显示清除数据对话框
func (a *App) ShowClearDataDialog() {
	if a.ctx != nil {
		// 发送事件通知前端显示清除数据对话框
		rt.EventsEmit(a.ctx, "show:clearData")
	}
}

// 获取目录大小
func getDirSize(path string) int64 {
	var size int64

	// 检查目录是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return 0
	}

	// 遍历目录计算大小
	filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})

	return size
}

// 获取文件大小
func getFileSize(path string) int64 {
	// 检查文件是否存在
	info, err := os.Stat(path)
	if err != nil {
		return 0
	}

	return info.Size()
}

// 清除目录内容
func clearDirectory(path string) error {
	// 检查目录是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil // 目录不存在，视为已清除
	}

	// 删除目录
	err := os.RemoveAll(path)
	if err != nil {
		return err
	}

	// 重新创建目录
	return os.MkdirAll(path, 0o755)
}

// ShowRechargeDialog 显示充值对话框
func (a *App) ShowRechargeDialog() {
	// 不再使用消息对话框，而是发送事件
	if a.ctx != nil {
		// 发送事件通知前端显示充值对话框
		rt.EventsEmit(a.ctx, "show:recharge")
	}
}

// SubmitRechargeOrder 提交充值订单
func (a *App) SubmitRechargeOrder(orderNumber string, amount float64) map[string]any {
	// 验证用户是否登录
	userEmail := GetConfig().GetString("app_username")
	if userEmail == "" {
		return map[string]any{
			"success": false,
			"message": "用户未登录，请先登录",
		}
	}

	if AppLogger != nil {
		AppLogger.Info("用户 %s 提交充值订单，金额: %.2f, 订单号: %s", userEmail, amount, orderNumber)
	}

	// 检查腾讯云客户端是否初始化
	if TencentCloudHttpClient == nil {
		if AppLogger != nil {
			AppLogger.Error("腾讯云客户端未初始化")
		}
		return map[string]any{
			"success": false,
			"message": "系统错误，请稍后重试",
		}
	}

	// 调用充值API
	response, err := TencentCloudHttpClient.SubmitRecharge(a.ctx, amount, orderNumber, "")
	if err != nil {
		if AppLogger != nil {
			AppLogger.Error("提交充值请求失败: %v", err)
		}

		// 处理特定错误码
		if err.Error() == "401" {
			return map[string]any{
				"success": false,
				"message": "登录已过期，请重新登录",
			}
		}

		return map[string]any{
			"success": false,
			"message": fmt.Sprintf("提交充值请求失败: %v", err),
		}
	}

	// 充值请求成功，返回响应
	if AppLogger != nil {
		AppLogger.Info("充值请求提交成功，充值ID: %d, 状态: %s",
			response.RechargeID, response.RequestInfo.Status)
	}

	return map[string]any{
		"success":     response.Success,
		"message":     response.Message,
		"recharge_id": response.RechargeID,
		"data": map[string]any{
			"email":       userEmail,
			"orderNumber": orderNumber,
			"amount":      amount,
			"status":      response.RequestInfo.Status,
			"timestamp":   time.Now().Format(time.RFC3339),
		},
	}
}

// GetComponentDownloadLink 获取组件下载链接
func (a *App) GetComponentDownloadLink() string {
	// 根据操作系统返回对应的下载链接
	switch runtime.GOOS {
	case "windows":
		return "https://dl.shanzhulab.cn/shanzhu-env-win.zip"
	case "darwin":
		return "https://dl.shanzhulab.cn/shanzhu-env-mac.zip"
	}
	return ""
}

// 计算平均分
func CalculateAverageScore(scores []int) float64 {
	var averageScore float64
	if len(scores) > 0 {
		sum := 0
		for _, score := range scores {
			sum += score
		}
		averageScore = float64(sum) / float64(len(scores))
	}
	return averageScore
}

// CheckUpdate 检查应用更新
// 返回: 无
func (a *App) CheckUpdate() {
	if a.ctx != nil {
		// 发送事件通知前端显示更新检查对话框
		rt.EventsEmit(a.ctx, "show:checkUpdate")
	}
}
