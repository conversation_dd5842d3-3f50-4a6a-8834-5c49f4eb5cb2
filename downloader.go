package main

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/duke-git/lancet/v2/fileutil"
	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

// DownloadProgress 下载进度信息
type DownloadProgress struct {
	Downloaded int64   `json:"downloaded"` // 已下载字节数
	Total      int64   `json:"total"`      // 总字节数
	Speed      int64   `json:"speed"`      // 下载速度 (字节/秒)
	Percentage float64 `json:"percentage"` // 下载百分比
	FileName   string  `json:"fileName"`   // 文件名
}

// ExtractProgress 结构体已移除，改为简单的loading状态。

// OverallProgress 总体进度信息
type OverallProgress struct {
	Stage      string  `json:"stage"`      // 当前阶段: "downloading", "extracting", "complete"
	Percentage float64 `json:"percentage"` // 总体百分比
	Message    string  `json:"message"`    // 状态消息
}

// ProgressWriter 实现带进度回调的写入器
type ProgressWriter struct {
	ctx        context.Context
	total      int64
	downloaded int64
	fileName   string
	lastUpdate time.Time
	lastBytes  int64
}

func NewProgressWriter(ctx context.Context, total int64, fileName string) *ProgressWriter {
	return &ProgressWriter{
		ctx:        ctx,
		total:      total,
		fileName:   fileName,
		lastUpdate: time.Now(),
	}
}

func (pw *ProgressWriter) Write(p []byte) (int, error) {
	n := len(p)
	pw.downloaded += int64(n)

	now := time.Now()
	// 每100ms更新一次进度
	if now.Sub(pw.lastUpdate) >= 100*time.Millisecond {
		// 计算下载速度
		timeDiff := now.Sub(pw.lastUpdate).Seconds()
		bytesDiff := pw.downloaded - pw.lastBytes
		speed := int64(float64(bytesDiff) / timeDiff)

		percentage := float64(pw.downloaded) / float64(pw.total) * 100

		progress := DownloadProgress{
			Downloaded: pw.downloaded,
			Total:      pw.total,
			Speed:      speed,
			Percentage: percentage,
			FileName:   pw.fileName,
		}
		// 发送下载进度事件
		rt.EventsEmit(pw.ctx, "download:progress", progress)
		pw.lastUpdate = now
		pw.lastBytes = pw.downloaded
	}

	return n, nil
}

// downloadFileWithProgress 下载文件并显示进度
func downloadFileWithProgress(ctx context.Context, url, destPath string) error {
	if AppLogger != nil {
		AppLogger.Info("开始下载文件: %s", url)
	}
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}
	client := &http.Client{
		Timeout: 30 * time.Minute, // 30分钟超时
	}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("下载失败: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}
	// 获取文件大小
	contentLength := resp.ContentLength
	if contentLength <= 0 {
		return fmt.Errorf("无法获取文件大小")
	}
	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(destPath), 0o755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}
	// 创建目标文件
	file, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()
	// 创建进度写入器
	fileName := filepath.Base(destPath)
	progressWriter := NewProgressWriter(ctx, contentLength, fileName)
	// 使用MultiWriter同时写入文件和进度
	writer := io.MultiWriter(file, progressWriter)
	// 开始下载
	_, err = io.Copy(writer, resp.Body)
	if err != nil {
		return fmt.Errorf("下载文件失败: %v", err)
	}
	if AppLogger != nil {
		AppLogger.Info("文件下载完成: %s", destPath)
	}
	return nil
}

// extractZipWithLancet 使用lancet库解压ZIP文件
func extractZipWithLancet(ctx context.Context, zipPath, destPath string) error {
	if AppLogger != nil {
		AppLogger.Info("开始解压文件: %s", zipPath)
	}
	// 发送解压开始事件（显示loading状态）
	rt.EventsEmit(ctx, "extract:start")
	// 使用lancet库解压文件
	err := fileutil.UnZip(zipPath, destPath)
	if err != nil {
		return fmt.Errorf("解压文件失败: %v", err)
	}
	if AppLogger != nil {
		AppLogger.Info("文件解压完成: %s", destPath)
	}
	return nil
}

// tryDownloadFromMirrors 尝试从多个镜像下载文件
func tryDownloadFromMirrors(ctx context.Context, urls []string, destPath string) error {
	var lastErr error

	for i, url := range urls {
		if AppLogger != nil {
			AppLogger.Info("尝试从镜像 %d/%d 下载: %s", i+1, len(urls), url)
		}

		// 发送当前镜像信息
		rt.EventsEmit(ctx, "download:mirror", map[string]any{
			"current": i + 1,
			"total":   len(urls),
			"url":     url,
		})

		err := downloadFileWithProgress(ctx, url, destPath)
		if err == nil {
			return nil // 下载成功
		}

		lastErr = err
		if AppLogger != nil {
			AppLogger.Warning("镜像 %d 下载失败: %v", i+1, err)
		}

		// 如果不是最后一个镜像，等待一下再尝试下一个
		if i < len(urls)-1 {
			time.Sleep(1 * time.Second)
		}
	}

	return fmt.Errorf("所有镜像下载失败，最后错误: %v", lastErr)
}
