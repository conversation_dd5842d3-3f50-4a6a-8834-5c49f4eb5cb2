package main

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

// DownloadProgress 下载进度信息
type DownloadProgress struct {
	Downloaded int64   `json:"downloaded"` // 已下载字节数
	Total      int64   `json:"total"`      // 总字节数
	Speed      int64   `json:"speed"`      // 下载速度 (字节/秒)
	Percentage float64 `json:"percentage"` // 下载百分比
	FileName   string  `json:"fileName"`   // 文件名
}

// ExtractProgress 解压进度信息
type ExtractProgress struct {
	Current    int     `json:"current"`    // 当前处理的文件数
	Total      int     `json:"total"`      // 总文件数
	Percentage float64 `json:"percentage"` // 解压百分比
	FileName   string  `json:"fileName"`   // 当前处理的文件名
}

// OverallProgress 总体进度信息
type OverallProgress struct {
	Stage      string  `json:"stage"`      // 当前阶段: "downloading", "extracting", "complete"
	Percentage float64 `json:"percentage"` // 总体百分比
	Message    string  `json:"message"`    // 状态消息
}

// ProgressWriter 实现带进度回调的写入器
type ProgressWriter struct {
	ctx        context.Context
	total      int64
	downloaded int64
	fileName   string
	lastUpdate time.Time
	lastBytes  int64
}

func NewProgressWriter(ctx context.Context, total int64, fileName string) *ProgressWriter {
	return &ProgressWriter{
		ctx:        ctx,
		total:      total,
		fileName:   fileName,
		lastUpdate: time.Now(),
	}
}

func (pw *ProgressWriter) Write(p []byte) (int, error) {
	n := len(p)
	pw.downloaded += int64(n)

	now := time.Now()
	// 每100ms更新一次进度
	if now.Sub(pw.lastUpdate) >= 100*time.Millisecond {
		// 计算下载速度
		timeDiff := now.Sub(pw.lastUpdate).Seconds()
		bytesDiff := pw.downloaded - pw.lastBytes
		speed := int64(float64(bytesDiff) / timeDiff)

		percentage := float64(pw.downloaded) / float64(pw.total) * 100

		progress := DownloadProgress{
			Downloaded: pw.downloaded,
			Total:      pw.total,
			Speed:      speed,
			Percentage: percentage,
			FileName:   pw.fileName,
		}
		// 发送下载进度事件
		rt.EventsEmit(pw.ctx, "download:progress", progress)
		pw.lastUpdate = now
		pw.lastBytes = pw.downloaded
	}

	return n, nil
}

// downloadFileWithProgress 下载文件并显示进度
func downloadFileWithProgress(ctx context.Context, url, destPath string) error {
	if AppLogger != nil {
		AppLogger.Info("开始下载文件: %s", url)
	}
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}
	client := &http.Client{
		Timeout: 30 * time.Minute, // 30分钟超时
	}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("下载失败: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}
	// 获取文件大小
	contentLength := resp.ContentLength
	if contentLength <= 0 {
		return fmt.Errorf("无法获取文件大小")
	}
	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(destPath), 0o755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}
	// 创建目标文件
	file, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()
	// 创建进度写入器
	fileName := filepath.Base(destPath)
	progressWriter := NewProgressWriter(ctx, contentLength, fileName)
	// 使用MultiWriter同时写入文件和进度
	writer := io.MultiWriter(file, progressWriter)
	// 开始下载
	_, err = io.Copy(writer, resp.Body)
	if err != nil {
		return fmt.Errorf("下载文件失败: %v", err)
	}
	if AppLogger != nil {
		AppLogger.Info("文件下载完成: %s", destPath)
	}
	return nil
}

// extractZipWithProgress 解压ZIP文件并显示进度
func extractZipWithProgress(ctx context.Context, zipPath, destPath string) error {
	if AppLogger != nil {
		AppLogger.Info("开始解压文件: %s", zipPath)
	}
	// 打开ZIP文件
	r, err := zip.OpenReader(zipPath)
	if err != nil {
		return fmt.Errorf("无法打开ZIP文件: %v", err)
	}
	defer r.Close()
	// 计算总文件数
	totalFiles := len(r.File)
	// 发送解压开始事件
	rt.EventsEmit(ctx, "extract:start", totalFiles)
	// 遍历ZIP文件中的所有文件
	for i, f := range r.File {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		// 构建目标文件路径
		fpath := filepath.Join(destPath, f.Name)
		// 检查路径是否在目标目录内（防止目录穿越攻击）
		if !strings.HasPrefix(fpath, filepath.Clean(destPath)+string(os.PathSeparator)) {
			return fmt.Errorf("非法的文件路径: %s", f.Name)
		}

		// 发送解压进度
		progress := ExtractProgress{
			Current:    i + 1,
			Total:      totalFiles,
			Percentage: float64(i+1) / float64(totalFiles) * 100,
			FileName:   f.Name,
		}
		rt.EventsEmit(ctx, "extract:progress", progress)

		if f.FileInfo().IsDir() {
			// 创建目录
			os.MkdirAll(fpath, f.Mode())
			continue
		}

		// 确保父目录存在
		if err := os.MkdirAll(filepath.Dir(fpath), 0o755); err != nil {
			return fmt.Errorf("创建目录失败: %v", err)
		}

		// 创建目标文件
		outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
		if err != nil {
			return fmt.Errorf("创建文件失败: %v", err)
		}

		// 打开ZIP中的源文件
		src, err := f.Open()
		if err != nil {
			outFile.Close()
			return fmt.Errorf("打开ZIP中的文件失败: %v", err)
		}

		// 复制文件内容
		_, err = io.Copy(outFile, src)
		src.Close()
		outFile.Close()

		if err != nil {
			return fmt.Errorf("复制文件内容失败: %v", err)
		}
	}

	if AppLogger != nil {
		AppLogger.Info("文件解压完成: %s", destPath)
	}

	return nil
}

// tryDownloadFromMirrors 尝试从多个镜像下载文件
func tryDownloadFromMirrors(ctx context.Context, urls []string, destPath string) error {
	var lastErr error

	for i, url := range urls {
		if AppLogger != nil {
			AppLogger.Info("尝试从镜像 %d/%d 下载: %s", i+1, len(urls), url)
		}

		// 发送当前镜像信息
		rt.EventsEmit(ctx, "download:mirror", map[string]any{
			"current": i + 1,
			"total":   len(urls),
			"url":     url,
		})

		err := downloadFileWithProgress(ctx, url, destPath)
		if err == nil {
			return nil // 下载成功
		}

		lastErr = err
		if AppLogger != nil {
			AppLogger.Warning("镜像 %d 下载失败: %v", i+1, err)
		}

		// 如果不是最后一个镜像，等待一下再尝试下一个
		if i < len(urls)-1 {
			time.Sleep(1 * time.Second)
		}
	}

	return fmt.Errorf("所有镜像下载失败，最后错误: %v", lastErr)
}
