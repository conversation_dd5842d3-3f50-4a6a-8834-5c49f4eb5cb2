package main

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/adrg/xdg"
	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

var (
	driverVersion    = "1.52.0"
	driverCDNMirrors = []string{
		"https://dl.shanzhulab.cn",
		"https://playwright.azureedge.net",
		"https://playwright-akamai.azureedge.net",
		"https://playwright-verizon.azureedge.net",
	}
	browserCDNMirrors = []string{
		"https://registry.npmmirror.com/-/binary/playwright",
		"https://cdn.playwright.dev/dbazure/download/playwright",
		"https://dl.shanzhulab.cn",
	}
)

// InstallPlaywrightWithProgress 安装组件并显示进度
func InstallPlaywrightWithProgress(ctx context.Context) error {
	if AppLogger != nil {
		AppLogger.Info("开始安装组件")
	}
	// 获取安装路径
	installPath, err := getPlaywrightPath()
	if err != nil {
		return fmt.Errorf("获取安装路径失败: %v", err)
	}
	// 创建临时目录用于下载
	tempDir := filepath.Join(installPath, "temp")
	if err := os.MkdirAll(tempDir, 0o755); err != nil {
		return fmt.Errorf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir) // 清理临时目录
	// 发送总体进度开始事件
	rt.EventsEmit(ctx, "install:start")
	// 1. 下载driver
	rt.EventsEmit(ctx, "install:overall", OverallProgress{
		Stage:      "downloading",
		Percentage: 0,
		Message:    "正在下载驱动程序...",
	})
	driverURLs := GetDriverURLs()
	if len(driverURLs) == 0 {
		return fmt.Errorf("没有可用的驱动程序下载链接")
	}
	driverZipPath := filepath.Join(tempDir, "playwright-driver.zip")
	if err := tryDownloadFromMirrors(ctx, driverURLs, driverZipPath); err != nil {
		return fmt.Errorf("下载驱动程序失败: %v", err)
	}
	// 2. 下载browser
	rt.EventsEmit(ctx, "install:overall", OverallProgress{
		Stage:      "downloading",
		Percentage: 25,
		Message:    "正在下载浏览器...",
	})
	browserURLs := GetBrwoserUrls()
	if len(browserURLs) == 0 {
		return fmt.Errorf("没有可用的浏览器下载链接")
	}
	browserZipPath := filepath.Join(tempDir, "chromium-browser.zip")
	if err := tryDownloadFromMirrors(ctx, browserURLs, browserZipPath); err != nil {
		return fmt.Errorf("下载浏览器失败: %v", err)
	}
	// 3. 解压driver
	rt.EventsEmit(ctx, "install:overall", OverallProgress{
		Stage:      "extracting",
		Percentage: 50,
		Message:    "正在解压驱动程序...",
	})
	if err := extractZipWithProgress(ctx, driverZipPath, installPath); err != nil {
		return fmt.Errorf("解压驱动程序失败: %v", err)
	}
	// 4. 解压browser
	rt.EventsEmit(ctx, "install:overall", OverallProgress{
		Stage:      "extracting",
		Percentage: 75,
		Message:    "正在解压浏览器...",
	})
	if err := extractZipWithProgress(ctx, browserZipPath, installPath); err != nil {
		return fmt.Errorf("解压浏览器失败: %v", err)
	}
	// 5. 完成安装
	rt.EventsEmit(ctx, "install:overall", OverallProgress{
		Stage:      "complete",
		Percentage: 100,
		Message:    "安装完成",
	})
	if AppLogger != nil {
		AppLogger.Info("组件安装完成")
	}
	return nil
}

func GetDriverPath() (string, error) {
	path, err := getPlaywrightPath()
	if err != nil {
		return "", err
	}
	return filepath.Join(path, "ms-playwright-go", driverVersion), nil
}

func GetBrowserPath() (string, error) {
	path, err := getPlaywrightPath()
	if err != nil {
		return "", err
	}
	return filepath.Join(path, "ms-playwright", "chromium-1169"), nil
}

// Default is user cache directory
func getPlaywrightPath() (string, error) {
	switch runtime.GOOS {
	case "windows":
		return filepath.Join(xdg.Home, "AppData", "Local"), nil
	case "darwin":
		return xdg.CacheHome, nil
	default:
		return "", fmt.Errorf("unsupported os: %s", runtime.GOOS)
	}
}

func GetDriverURLs() []string {
	platform := ""
	switch runtime.GOOS {
	case "windows":
		platform = "win32_x64"
	case "darwin":
		if runtime.GOARCH == "arm64" {
			platform = "mac-arm64"
		} else {
			platform = "mac"
		}
	case "linux":
		if runtime.GOARCH == "arm64" {
			platform = "linux-arm64"
		} else {
			platform = "linux"
		}
	default:
		return []string{}
	}
	baseURLs := []string{}
	pattern := "%s/builds/driver/playwright-%s-%s.zip"
	if hostEnv := os.Getenv("PLAYWRIGHT_DOWNLOAD_HOST"); hostEnv != "" {
		baseURLs = append(baseURLs, fmt.Sprintf(pattern, hostEnv, driverVersion, platform))
	} else {
		for _, mirror := range driverCDNMirrors {
			baseURLs = append(baseURLs, fmt.Sprintf(pattern, mirror, driverVersion, platform))
		}
	}
	return baseURLs
}

func GetBrwoserUrls() []string {
	platform := ""
	switch runtime.GOOS {
	case "windows":
		platform = "win64"
	case "darwin":
		if runtime.GOARCH == "arm64" {
			platform = "mac-arm64"
		}
	default:
		return []string{}
	}
	baseURLs := []string{}
	pattern := "%s/builds/chromium/1169/chromium-%s.zip"
	if hostEnv := os.Getenv("PLAYWRIGHT_DOWNLOAD_HOST"); hostEnv != "" {
		baseURLs = append(baseURLs, fmt.Sprintf(pattern, hostEnv, platform))
	} else {
		for _, mirror := range browserCDNMirrors {
			baseURLs = append(baseURLs, fmt.Sprintf(pattern, mirror, platform))
		}
	}
	return baseURLs
}
