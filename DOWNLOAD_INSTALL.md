# 自定义下载和安装功能

本项目实现了一个自定义的下载和安装系统，用于替代 `playwright.Install()` 函数，提供更好的用户体验和进度显示。

## 功能特性

### 1. 实时进度显示
- **下载进度**: 显示下载速度、已下载大小、总大小和百分比
- **解压进度**: 显示当前处理的文件数、总文件数和百分比  
- **总体进度**: 显示当前阶段（下载/解压）和总体完成百分比

### 2. 多镜像支持
- 自动尝试多个下载镜像
- 镜像失败时自动切换到下一个
- 显示当前使用的镜像信息

### 3. 用户友好的界面
- 在线安装：自动下载最新组件
- 离线安装：支持手动选择安装包
- 实时状态更新和错误提示

## 技术实现

### 后端组件

#### 1. `downloader.go`
核心下载器实现，包含：
- `ProgressWriter`: 实现带进度回调的写入器
- `downloadFileWithProgress()`: 下载文件并显示进度
- `extractZipWithProgress()`: 解压ZIP文件并显示进度
- `tryDownloadFromMirrors()`: 尝试从多个镜像下载

#### 2. `playwright.go`
更新的Playwright安装功能：
- `InstallPlaywrightWithProgress()`: 带进度的安装函数
- 支持驱动程序和浏览器的分别下载和解压
- 完整的错误处理和状态报告

#### 3. `app.go`
应用层接口：
- 更新 `InstallPlaywright()` 方法使用新的安装功能
- 集成到现有的应用架构中

### 前端组件

#### 1. `InstallProgressDialog.tsx`
新的安装进度对话框：
- 实时显示下载和解压进度
- 格式化文件大小和下载速度
- 支持取消和错误处理

#### 2. `PlaywrightInstallDialog.tsx`
更新的安装选择对话框：
- 提供在线和离线安装选项
- 集成新的进度对话框
- 改进的用户界面

## 事件系统

### 后端事件
- `install:start`: 安装开始
- `install:overall`: 总体进度更新
- `download:progress`: 下载进度更新
- `download:mirror`: 当前镜像信息
- `extract:start`: 解压开始
- `extract:progress`: 解压进度更新

### 前端监听
前端组件监听这些事件并实时更新UI，提供流畅的用户体验。

## 数据结构

### DownloadProgress
```go
type DownloadProgress struct {
    Downloaded int64   // 已下载字节数
    Total      int64   // 总字节数
    Speed      int64   // 下载速度 (字节/秒)
    Percentage float64 // 下载百分比
    FileName   string  // 文件名
}
```

### ExtractProgress
```go
type ExtractProgress struct {
    Current    int     // 当前处理的文件数
    Total      int     // 总文件数
    Percentage float64 // 解压百分比
    FileName   string  // 当前处理的文件名
}
```

### OverallProgress
```go
type OverallProgress struct {
    Stage      string  // 当前阶段: "downloading", "extracting", "complete"
    Percentage float64 // 总体百分比
    Message    string  // 状态消息
}
```

## 安装流程

1. **初始化**: 创建临时目录，发送安装开始事件
2. **下载驱动**: 从镜像列表下载Playwright驱动程序
3. **下载浏览器**: 从镜像列表下载Chromium浏览器
4. **解压驱动**: 解压驱动程序到指定目录
5. **解压浏览器**: 解压浏览器到指定目录
6. **完成**: 清理临时文件，发送完成事件

## 错误处理

- 网络错误：自动尝试下一个镜像
- 文件错误：显示详细错误信息
- 用户取消：支持安装过程中的取消操作
- 超时处理：设置合理的下载超时时间

## 性能优化

- 分块下载：使用流式下载减少内存占用
- 进度节流：每100ms更新一次进度，避免过度刷新
- 并发控制：合理控制下载和解压的并发度
- 资源清理：及时清理临时文件和资源

## 使用方法

### 在线安装
1. 点击"在线安装"按钮
2. 系统自动下载和安装组件
3. 实时查看进度和状态
4. 安装完成后自动关闭对话框

### 离线安装
1. 点击"离线安装"按钮
2. 下载对应平台的安装包
3. 选择下载的ZIP文件
4. 系统自动解压到指定目录

## 平台支持

- **Windows**: 支持 win32_x64 架构
- **macOS**: 支持 mac 和 mac-arm64 架构
- **Linux**: 支持 linux 和 linux-arm64 架构

## 镜像配置

系统预配置了多个下载镜像：
- 山竹实验室镜像（主要）
- Playwright官方镜像
- Azure CDN镜像
- NPM镜像

可通过环境变量 `PLAYWRIGHT_DOWNLOAD_HOST` 自定义下载源。
